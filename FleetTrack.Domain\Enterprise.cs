﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class Enterprise
{
    public int EnterpriseID { get; set; }

    public string EnterpriseName { get; set; } = null!;

    public string? EnterpriseCode { get; set; }

    public bool IsActive { get; set; }

    public int? ParentEnterpriseID { get; set; }

    public string? SCAC { get; set; }

    public string? FederalTaxID { get; set; }

    public bool IsERPOnly { get; set; }

    public bool IsUsedForBookingHauling { get; set; }

    public int? LocationID { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public DateTime? InactiveDate { get; set; }

    public int? MailingLocationID { get; set; }

    public string? DoingBusinessAs { get; set; }

    public string? DOTNumber { get; set; }

    public virtual ICollection<AssetInspection> AssetInspections { get; set; } = new List<AssetInspection>();

    public virtual ICollection<Asset> Assets { get; set; } = new List<Asset>();

    public virtual ICollection<FacilityLocation> FacilityLocations { get; set; } = new List<FacilityLocation>();

    public virtual ICollection<Enterprise> InverseParentEnterprise { get; set; } = new List<Enterprise>();

    public virtual Enterprise? ParentEnterprise { get; set; }
}