﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentSecureLocationType
{
    public int EquipmentSecureLocationTypeID { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual ICollection<EquipmentSecureLocation> EquipmentSecureLocations { get; set; } = new List<EquipmentSecureLocation>();
}