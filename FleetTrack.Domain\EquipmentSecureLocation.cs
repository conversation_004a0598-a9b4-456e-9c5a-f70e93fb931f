﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentSecureLocation
{
    public int EquipmentSecureLocationID { get; set; }

    public int EquipmentID { get; set; }

    public int? EquipmentSecureLocationTypeID { get; set; }

    public int LocationID { get; set; }

    public bool IsCompanyLocation { get; set; }

    public string? ApprovedBy { get; set; }

    public DateTime? LastValidationDate { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual Equipment Equipment { get; set; } = null!;

    public virtual EquipmentSecureLocationType? EquipmentSecureLocationType { get; set; }

    public virtual Location Location { get; set; } = null!;
}