﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using NetTopologySuite.Geometries;

namespace FleetTrack.Domain;

public partial class CityPostCode
{
    public int CityPostCodeID { get; set; }

    public int CountryID { get; set; }

    public int RegionID { get; set; }

    public int? SubregionID { get; set; }

    public string? Division { get; set; }

    public string CityName { get; set; } = null!;

    public string PostCode { get; set; } = null!;

    public string? AddOnCode { get; set; }

    public Geometry? CityLocationInfo { get; set; }

    public Geometry? PostCodeLocationInfo { get; set; }

    public int? ModifiedByID { get; set; }

    public DateTime CreatedDate { get; set; }

    public int? CreatedByID { get; set; }

    public bool IsVerified { get; set; }

    public virtual Country Country { get; set; } = null!;

    public virtual ICollection<Location> Locations { get; set; } = new List<Location>();

    public virtual Region Region { get; set; } = null!;
}