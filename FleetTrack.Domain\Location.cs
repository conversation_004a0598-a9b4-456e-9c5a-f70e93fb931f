﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using NetTopologySuite.Geometries;

namespace FleetTrack.Domain;

public partial class Location
{
    public int LocationID { get; set; }

    public string? AltID { get; set; }

    public string? Name { get; set; }

    public string AddressLine { get; set; } = null!;

    public string? AddressLine2 { get; set; }

    public int? CityPostCodeID { get; set; }

    public Geometry? LocationInfo { get; set; }

    public int? ModifiedByID { get; set; }

    public short? TimeZoneID { get; set; }

    public DateTime CreatedDate { get; set; }

    public int? CreatedByID { get; set; }

    public bool IsVerified { get; set; }

    public virtual CityPostCode? CityPostCode { get; set; }

    public virtual ICollection<EquipmentSecureLocation> EquipmentSecureLocations { get; set; } = new List<EquipmentSecureLocation>();

    public virtual ICollection<FacilityLocation> FacilityLocationLocations { get; set; } = new List<FacilityLocation>();

    public virtual ICollection<FacilityLocation> FacilityLocationMailingLocations { get; set; } = new List<FacilityLocation>();
}