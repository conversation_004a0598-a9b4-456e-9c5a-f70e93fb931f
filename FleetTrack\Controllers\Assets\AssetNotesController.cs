using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using FleetTrack.Models.Data;
using FleetTrack.Data.Atlas;
using System.Security.Claims;
using FleetTrack.Services.Assets;
using FleetTrack.Models.Assets;

//using FleetTrack.Utilities;

namespace FleetTrack.Controllers.Assets
{
    [ApiController]
    [Route("api/Assets/[controller]")]
    public class AssetNotesController : ControllerBase
    {
        private readonly ILogger<AssetNotesController> _logger;
        private readonly IAssetNoteService _assetNoteService;
        private readonly AtlasContext _context;

        public AssetNotesController(
            ILogger<AssetNotesController> logger,
            IAssetNoteService assetNoteService,
            AtlasContext context)
        {
            _logger = logger;
            _assetNoteService = assetNoteService;
            _context = context;
        }

        [HttpGet("notes/{id}")]
        public async Task<IActionResult> GetAssetNotes(int id)
        {
            _logger.LogInformation($"GetAssetNotes called for AssetID: {id}");
            try
            {
                var result = await _assetNoteService.GetAssetNotesDTO(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetAssetNotes for AssetID: {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("note/{id}")]
        public async Task<IActionResult> GetAssetNoteById(int id)
        {
            try
            {
                var note = await _assetNoteService.GetAssetNoteById(id);
                if (note == null)
                {
                    return NotFound($"Note with ID {id} not found");
                }
                
                return Ok(note);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in GetAssetNoteById for NoteID: {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("note")]
        public async Task<IActionResult> CreateAssetNote([FromBody] SelectedAssetNoteDTO noteDto)
        {
            try
            {
                var personnelAccessID = Utilities.Claims.GetPersonnelAccessID(HttpContext.User.Identity as ClaimsIdentity);
                var noteId = await _assetNoteService.CreateAssetNote(noteDto, personnelAccessID);
                
                return Ok(new { NoteID = noteId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in CreateAssetNote for AssetID: {noteDto.AssetID}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("note")]
        public async Task<IActionResult> UpdateAssetNote([FromBody] SelectedAssetNoteDTO noteDto)
        {
            try
            {
                var personnelAccessID = Utilities.Claims.GetPersonnelAccessID(HttpContext.User.Identity as ClaimsIdentity);
                var result = await _assetNoteService.UpdateAssetNote(noteDto, personnelAccessID);
                
                if (!result)
                {
                    return NotFound($"Note with ID {noteDto.NoteID} not found");
                }
                
                return Ok(new { Success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in UpdateAssetNote for NoteID: {noteDto.NoteID}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("note/{id}")]
        public async Task<IActionResult> DeleteAssetNote(int id)
        {
            try
            {
                var result = await _assetNoteService.DeleteAssetNote(id);
                
                if (!result)
                {
                    return NotFound($"Note with ID {id} not found");
                }
                
                return Ok(new { Success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in DeleteAssetNote for NoteID: {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("noteTypes")]
        public async Task<IActionResult> GetAssetNoteTypes()
        {
            _logger.LogInformation("AssetNotesController.GetAssetNoteTypes called");
            try
            {
                var noteTypes = await _assetNoteService.GetAssetNoteTypes();
                
                // Log success and return count
                _logger.LogInformation($"Successfully retrieved {noteTypes?.Count ?? 0} note types");
                
                // Return empty array instead of null
                return Ok(noteTypes ?? new List<NoteTypeDTO>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in AssetNotesController.GetAssetNoteTypes: {Message}", ex.Message);
                
                // Log more detailed error info
                if (ex.InnerException != null)
                {
                    _logger.LogError("Inner exception: {Message}", ex.InnerException.Message);
                }
                
                // Return a more detailed error response
                return StatusCode(500, new { 
                    Error = "Error retrieving note types", 
                    ex.Message,
                    InnerMessage = ex.InnerException?.Message
                });
            }
        }

        [HttpPut("noteTemp")]
        public async Task<IActionResult> UpdateAssetNoteTemp([FromBody] SelectedAssetNoteDTO noteDto)
        {
            _logger.LogInformation($"UpdateAssetNoteTemp called for NoteID: {noteDto?.NoteID}");

            if (noteDto == null || noteDto.NoteID <= 0)
            {
                return BadRequest("Invalid note data");
            }

            try
            {
                // Get the personnel access ID from claims
                int personnelAccessID = Utilities.Claims.GetPersonnelAccessID(HttpContext.User.Identity as ClaimsIdentity);
                
                // Set the ModifiedByID on the DTO before updating
                noteDto.ModifiedByID = personnelAccessID;

                // Call the service to update the note
                var result = await _assetNoteService.UpdateAssetNote(noteDto, personnelAccessID);
                
                if (!result)
                {
                    return NotFound($"Note with ID {noteDto.NoteID} not found");
                }
                
                return Ok(new { 
                    Success = true,
                    ModifiedByID = personnelAccessID
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in UpdateAssetNoteTemp for NoteID: {noteDto?.NoteID}: {ex.Message}, Inner: {ex.InnerException?.Message}");
                return StatusCode(500, $"Internal server error: {ex.Message}, Inner: {ex.InnerException?.Message}");
            }
        }

        [HttpPut("updateNoteText")]
        public async Task<IActionResult> UpdateNoteTextOnly([FromBody] UpdateNoteTextRequest request)
        {
            _logger.LogInformation($"UpdateNoteTextOnly called for NoteID: {request?.NoteID}");
            
            // Input validation
            if (request == null)
            {
                _logger.LogWarning("Invalid data received: Request is null");
                return BadRequest("Request cannot be null");
            }
            
            try
            {
                // Get the personnel access ID from claims
                var personnelAccessID = Utilities.Claims.GetPersonnelAccessID(HttpContext.User.Identity as ClaimsIdentity);
                
                // Create a DTO from the request
                var noteDto = new SelectedAssetNoteDTO
                {
                    NoteID = request.NoteID,
                    NoteText = request.NoteText,
                    ModifiedByID = personnelAccessID
                };
                
                // Call the service to update the note
                var result = await _assetNoteService.UpdateAssetNote(noteDto, personnelAccessID);
                
                if (!result)
                {
                    return NotFound($"Note with ID {request.NoteID} not found");
                }
                
                return Ok(new { 
                    Success = true,
                    ModifiedByID = personnelAccessID
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error in UpdateNoteTextOnly for NoteID: {request?.NoteID}: {ex.Message}, Inner: {ex.InnerException?.Message}");
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
    }

    public class UpdateNoteTextRequest
    {
        public int NoteID { get; set; }
        public string NoteText { get; set; }
        public int ModifiedByID { get; set; }
    }
} 