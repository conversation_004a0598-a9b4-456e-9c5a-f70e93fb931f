﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class AssetNote
{
    public int AssetNoteID { get; set; }

    public int AssetID { get; set; }

    public string Note { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public int AssetNoteTypeID { get; set; }

    public virtual Asset Asset { get; set; } = null!;
}