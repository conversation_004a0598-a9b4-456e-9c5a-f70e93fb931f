﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentCategory
{
    public int EquipmentCategoryID { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual ICollection<EquipmentType> EquipmentTypes { get; set; } = new List<EquipmentType>();

    public virtual ICollection<Plate> Plates { get; set; } = new List<Plate>();
}