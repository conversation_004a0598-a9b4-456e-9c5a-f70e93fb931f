﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class Asset
{
    public int AssetID { get; set; }

    public int AssetTypeID { get; set; }

    public int AssetOwnerTypeID { get; set; }

    public int? OwnedByContractorID { get; set; }

    public string AssetTag { get; set; } = null!;

    public int? ParentAssetID { get; set; }

    public DateTime? AcquisitionDate { get; set; }

    public decimal? AssetCostAmount { get; set; }

    public bool IsActive { get; set; }

    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public int? OwnedByEnterpriseID { get; set; }

    public int? DomicileFacilityLocationID { get; set; }

    public bool? IsUsable { get; set; }

    public int? AssetStatusID { get; set; }

    public virtual ICollection<AssetInspection> AssetInspections { get; set; } = new List<AssetInspection>();

    public virtual ICollection<AssetNote> AssetNotes { get; set; } = new List<AssetNote>();

    public virtual AssetOwnerType AssetOwnerType { get; set; } = null!;

    public virtual FacilityLocation? DomicileFacilityLocation { get; set; }

    public virtual ICollection<Equipment> Equipment { get; set; } = new List<Equipment>();

    public virtual ICollection<Asset> InverseParentAsset { get; set; } = new List<Asset>();

    public virtual Enterprise? OwnedByEnterprise { get; set; }

    public virtual Asset? ParentAsset { get; set; }
}