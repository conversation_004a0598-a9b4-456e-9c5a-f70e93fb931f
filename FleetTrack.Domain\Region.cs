﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using NetTopologySuite.Geometries;

namespace FleetTrack.Domain;

public partial class Region
{
    public int RegionID { get; set; }

    public int? CountryID { get; set; }

    public string? Name { get; set; }

    public Geometry? LocationInfo { get; set; }

    public int ModifiedByID { get; set; }

    public string? Abbreviation { get; set; }

    public DateTime CreatedDate { get; set; }

    public int? CreatedByID { get; set; }

    public bool IsVerified { get; set; }

    public bool HasPermit { get; set; }

    public virtual ICollection<CityPostCode> CityPostCodes { get; set; } = new List<CityPostCode>();

    public virtual Country? Country { get; set; }

    public virtual ICollection<Plate> Plates { get; set; } = new List<Plate>();
}