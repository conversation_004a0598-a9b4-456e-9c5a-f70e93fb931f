﻿//using System;

//namespace FleetTrack.Models.Enterprises
//{
//	public class EnterpriseSearchResult
//	{
//		public int EnterpriseID { get; set; }
//		public string? EnterpriseCode { get; set; }
//		public string EnterpriseName { get; set; }
//		public string StatusName { get; set; }
//		public string? SCAC { get; set; }
//		public string? FederalTaxID { get; set; }
//		public bool IsERPOnly { get; set; }
//		public bool IsUsedForBookingHauling { get; set; }
//		public bool IsActive { get; set; }
//		public int ActiveFacilityCount { get; set; }
//		public string DoingBusinessAs { get; set; }
//		public int? LocationID { get; set; }
//		public int? MailingLocationID { get; set; }
//		public string? ValidFrom { get; set; }
//		public string? ValidTo { get; set; }

//		public string AddressLine1 { get; set; }
//		public string? AddressLine2 { get; set; }
//		public string CityName { get; set; }
//		public string Abbreviation { get; set; }
//		public string PostCode { get; set; }
//		public string Country { get; set; }
//		public string County { get; set; }

//		public string MailAddressLine1 { get; set; }
//		public string MailAddressLine2 { get; set; }
//		public string MailCityName { get; set; }
//		public string MailPostCode { get; set; }
//		public string MailAbbreviation { get; set; }
//		public string MailCountry { get; set; }
//		public string MailCounty { get; set; }
//	}

//	public class EnterpriseNoteSearchResult
//	{
//		public int EnterpriseNoteID { get; set; }
//		public int EnterpriseID { get; set; }
//		public string EnterpriseNoteText { get; set; }
//		public string EnterpriseNoteTypeName { get; set; }
//		public int? EnterpriseNoteTypeID { get; set; }
//		public string EnterpriseNoteDate { get; set; }
//	}
//}
