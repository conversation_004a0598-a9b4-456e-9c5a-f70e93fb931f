﻿using Autofac;
using FleetTrack.Repositories.Deduction;
using FleetTrack.Services.Deductions;
using FleetTrack.Mappers.Financials;
using FleetTrack.Repositories.Finance;

namespace FleetTrack.DependencyInjectionModules
{
	public class DeductionModule : Module
	{
		protected override void Load(ContainerBuilder containerBuilder)
		{
			containerBuilder.RegisterType<DeductionService>()
				.As<IDeductionService>()
				.SingleInstance();

			containerBuilder.RegisterType<DeductionRepository>()
				.As<IDeductionRepository>()
				.SingleInstance();
			
			containerBuilder.RegisterType<AssetRepository>()
				.As<IAssetRepository>()
				.SingleInstance();
			
			containerBuilder.RegisterType<DeductionTypeMapper>()
				.As<IDeductionTypeMapper>()
				.SingleInstance();
			
			containerBuilder.RegisterType<LineItemCodeMapper>()
				.As<ILineItemCodeMapper>()
				.SingleInstance();
			


		}
	}
}
