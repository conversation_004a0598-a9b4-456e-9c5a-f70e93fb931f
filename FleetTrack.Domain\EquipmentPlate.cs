﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentPlate
{
    public int EquipmentID { get; set; }

    public int PlateID { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual Equipment Equipment { get; set; } = null!;

    public virtual Plate Plate { get; set; } = null!;
}