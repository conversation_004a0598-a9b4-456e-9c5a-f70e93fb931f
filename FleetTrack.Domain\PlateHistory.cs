﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class PlateHistory
{
    public int PlateHistoryID { get; set; }

    public int PlateID { get; set; }

    public int EquipmentID { get; set; }

    public decimal? UnladenWeight { get; set; }

    public decimal? GrossWeight { get; set; }

    public DateTime PlateStartDate { get; set; }

    public DateTime PlateExpirationDate { get; set; }

    public bool Is2290Required { get; set; }

    public DateTime? Federal2290ReceivedDate { get; set; }

    public decimal PlateCostAmount { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual Equipment Equipment { get; set; } = null!;

    public virtual Plate Plate { get; set; } = null!;
}