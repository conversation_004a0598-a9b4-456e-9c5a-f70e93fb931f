﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class Plate
{
    public int PlateID { get; set; }

    public int RegionID { get; set; }

    public int EquipmentCategoryID { get; set; }

    public string PlateNumber { get; set; } = null!;

    public bool IsApportioned { get; set; }

    public bool IsCompanyPlate { get; set; }

    public decimal? UnladenWeight { get; set; }

    public decimal? GrossWeight { get; set; }

    public DateTime PlateStartDate { get; set; }

    public DateTime PlateExpirationDate { get; set; }

    public bool Is2290Required { get; set; }

    public DateTime? Federal2290ReceivedDate { get; set; }

    public decimal PlateCostAmount { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public decimal? WeeklyDeduction { get; set; }

    public virtual EquipmentCategory EquipmentCategory { get; set; } = null!;

    public virtual ICollection<EquipmentPlate> EquipmentPlates { get; set; } = new List<EquipmentPlate>();

    public virtual ICollection<PlateHistory> PlateHistories { get; set; } = new List<PlateHistory>();

    public virtual Region Region { get; set; } = null!;
}