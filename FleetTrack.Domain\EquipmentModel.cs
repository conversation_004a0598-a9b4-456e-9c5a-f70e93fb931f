﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentModel
{
    public int EquipmentModelID { get; set; }

    public int EquipmentMakeID { get; set; }

    public string? Name { get; set; }

    public string? Description { get; set; }

    public string? ModelYear { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual ICollection<Equipment> Equipment { get; set; } = new List<Equipment>();

    public virtual EquipmentMake EquipmentMake { get; set; } = null!;
}