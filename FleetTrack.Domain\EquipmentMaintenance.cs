﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentMaintenance
{
    public int EquipmentMaintenanceID { get; set; }

    public int EquipmentMaintenanceTypeID { get; set; }

    public int EquipmentID { get; set; }

    public DateTime MaintenanceDate { get; set; }

    public DateTime DateReceived { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual Equipment Equipment { get; set; } = null!;
}