﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentPulledAttribute
{
    public int EquipmentPulledAttributeID { get; set; }

    public decimal Length { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual ICollection<Equipment> Equipment { get; set; } = new List<Equipment>();
}