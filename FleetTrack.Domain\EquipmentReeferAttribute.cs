﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentReeferAttribute
{
    public int EquipmentReeferAttributeID { get; set; }

    public decimal TemperatureHigh { get; set; }

    public decimal TemperatureLow { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual ICollection<Equipment> Equipment { get; set; } = new List<Equipment>();
}