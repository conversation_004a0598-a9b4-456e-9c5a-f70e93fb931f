using FleetTrack.Data.Atlas;
using FleetTrack.Models.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SqlKata.Execution;
using FleetTrack.Models.Assets;
using Microsoft.EntityFrameworkCore.Internal;

namespace FleetTrack.Repositories.Finance
{
    public class AssetNoteRepository
    {
        private readonly AtlasContext _context;
        private readonly ILogger<AssetNoteRepository> _logger;
        private readonly QueryFactory _queryFactory;

        public AssetNoteRepository(AtlasContext context, ILogger<AssetNoteRepository> logger, QueryFactory queryFactory)
        {
            _context = context;
            _logger = logger;
            _queryFactory = queryFactory;
        }

        public async Task<List<SelectedAssetNoteDTO>> GetAssetNotes(int assetID)
        {
         
            try
            {
                var notes = await _context.AssetNotes
                    .Where(an => an.AssetID == assetID)
                    .Include(an => an.AssetNoteType)
										.Join(_context.PersonnelAccesses, an => an.CreatedByID, pa => pa.PersonnelAccessID, (an, pa) => new { an, pa })
                    .Join(_context.Personnel, x => x.pa.PersonnelID, p => p.PersonnelID, (x, p) => new { x.an, x.pa, p })
                    .OrderByDescending(x => x.an.CreatedDate)
                    .Select(x => new SelectedAssetNoteDTO
                    {
                        NoteID = x.an.AssetNoteID,
													AssetID = x.an.AssetID,
													NoteText = x.an.Note,
													CreatedDate = x.an.CreatedDate,
													CreatedByUserName = FormatPersonnelName(x.p.FirstName, x.p.MiddleName, x.p.LastName),
                        NoteTypeID = x.an.AssetNoteTypeID,
                        NoteType = x.an.AssetNoteType.AssetNoteTypeName
                    })
                    .ToListAsync();

                return notes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting notes for asset ID: {assetID}");
                throw;
            }
        }

        public async Task<SelectedAssetNoteDTO> GetAssetNoteById(int noteID)
        {
            _logger.LogInformation($"Getting note with ID: {noteID}");
            try
            {
                var noteQuery = await _context.AssetNotes
                    .Include(an => an.AssetNoteType)
                    .Join(_context.PersonnelAccesses, an => an.CreatedByID, pa => pa.PersonnelAccessID, (an, pa) => new { an, pa })
                    .Join(_context.Personnel, x => x.pa.PersonnelID, p => p.PersonnelID, (x, p) => new { x.an, x.pa, p })
                    .FirstOrDefaultAsync(x => x.an.AssetNoteID == noteID);

                if (noteQuery == null)
                {
                    _logger.LogWarning($"Note with ID {noteID} not found");
                    return null;
                }

                var noteDto = new SelectedAssetNoteDTO
                {
                    NoteID = noteQuery.an.AssetNoteID,
                    AssetID = noteQuery.an.AssetID,
                    NoteText = noteQuery.an.Note,
                    CreatedDate = noteQuery.an.CreatedDate,
                    CreatedByUserName = FormatPersonnelName(noteQuery.p.FirstName, noteQuery.p.MiddleName, noteQuery.p.LastName),
                    NoteTypeID = noteQuery.an.AssetNoteTypeID,
                    NoteType = noteQuery.an.AssetNoteType.AssetNoteTypeName
                };

                return noteDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting note with ID: {noteID}");
                throw;
            }
        }

        private static string FormatPersonnelName(string firstName, string middleName, string lastName)
        {
            if (string.IsNullOrEmpty(firstName) && string.IsNullOrEmpty(lastName))
                return "System";
                
            return firstName + 
                   (string.IsNullOrEmpty(middleName) ? " " : " " + middleName + " ") + 
                   lastName;
        }

        public async Task<int> CreateAssetNote(SelectedAssetNoteDTO noteDto, int personnelAccessID)
        {
            _logger.LogInformation($"Creating a new note for asset ID: {noteDto.AssetID}");
            try
            {
                var note = new AssetNote
                {
                    AssetID = noteDto.AssetID,
                    Note = noteDto.NoteText,
                    AssetNoteTypeID = noteDto.NoteTypeID,
                    CreatedDate = DateTime.UtcNow,
                    CreatedByID = personnelAccessID
                };

                _context.AssetNotes.Add(note);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"Created note with ID: {note.AssetNoteID} for asset ID: {noteDto.AssetID}");
                return note.AssetNoteID;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating note for asset ID: {noteDto.AssetID}");
                throw;
            }
        }

        public async Task<bool> UpdateAssetNote(SelectedAssetNoteDTO noteDto, int personnelAccessID)
        {
           
            try
            {
                var note = await _context.AssetNotes.FindAsync(noteDto.NoteID);
                if (note == null)
                {
                    _logger.LogWarning($"Note with ID {noteDto.NoteID} not found");
                    return false;
                }

                
                // Update the note text from NoteText (client-side) to Note (database field)
                if (!string.IsNullOrEmpty(noteDto.NoteText))
                {
                    note.Note = noteDto.NoteText;
                   
                }
                
                // Update note type if provided in the DTO
                if (noteDto.NoteTypeID > 0)
                {
                    note.AssetNoteTypeID = noteDto.NoteTypeID;
                   
                }
                
                // Set ModifiedByID directly now that we've added the property to the class
                note.ModifiedByID = personnelAccessID;
               
                // Save changes using Entity Framework
                await _context.SaveChangesAsync();
               
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating note with ID: {noteDto.NoteID}");
                throw;
            }
        }

        public async Task<bool> DeleteAssetNote(int noteID)
        {
            
            try
            {
                var note = await _context.AssetNotes.FindAsync(noteID);
                if (note == null)
                {
                    return false;
                }

                _context.AssetNotes.Remove(note);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting note with ID: {noteID}");
                throw;
            }
        }

        public async Task<List<AssetNoteType>> GetAssetNoteTypes()
        {
            try
            {
                // Use SqlKata to get all note types, always set IsActive to true for compatibility
                var noteTypes = await _queryFactory.Query("Finance.AssetNoteTypes")
                    .Select(
                        "AssetNoteTypeID",
                        "AssetNoteTypeName",
                        "AssetNoteTypeDescription",
                        "CreatedDate",
                        "CreatedByID",
                        "ModifiedByID"
                    )
                    .OrderBy("AssetNoteTypeName")
                    .GetAsync<AssetNoteType>();

                return noteTypes.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving asset note types: {Message}", ex.Message);
                if (ex.InnerException != null)
                {
                    _logger.LogError("Inner exception: {Message}", ex.InnerException.Message);
                }
                return null;
            }
        }
        
        private async Task<List<AssetNoteType>> GetAssetNoteTypesWithRawSql()
        {
            _logger.LogInformation("Using SqlKata fallback to query asset note types");
            try
            {
                // Check if IsActive column exists (optional, but always set IsActive to true for compatibility)
                var noteTypes = await _queryFactory.Query("Finance.AssetNoteTypes")
                    .Select(
                        "AssetNoteTypeID",
                        "AssetNoteTypeName",
                        "AssetNoteTypeDescription",
                        "CreatedDate",
                        "CreatedByID",
                        "ModifiedByID"
                    )
                    .OrderBy("AssetNoteTypeName")
                    .GetAsync<AssetNoteType>();

                _logger.LogInformation($"Retrieved {noteTypes.Count()} asset note types using SqlKata");
                return noteTypes.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing SqlKata query: {Message}", ex.Message);
                return null;
            }
        }
        
        // Add a method to check if a column exists
        private async Task<bool> CheckIfColumnExists(string schema, string tableName, string columnName)
        {
            try
            {
                string sql = @"
                    SELECT COUNT(1) 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = @schema 
                    AND TABLE_NAME = @tableName 
                    AND COLUMN_NAME = @columnName";
                
                using (var command = _context.Database.GetDbConnection().CreateCommand())
                {
                    command.CommandText = sql;
                    
                    // Create parameters
                    var schemaParam = command.CreateParameter();
                    schemaParam.ParameterName = "@schema";
                    schemaParam.Value = schema;
                    command.Parameters.Add(schemaParam);
                    
                    var tableNameParam = command.CreateParameter();
                    tableNameParam.ParameterName = "@tableName";
                    tableNameParam.Value = tableName;
                    command.Parameters.Add(tableNameParam);
                    
                    var columnNameParam = command.CreateParameter();
                    columnNameParam.ParameterName = "@columnName";
                    columnNameParam.Value = columnName;
                    command.Parameters.Add(columnNameParam);
                    
                    // Ensure connection is open
                    if (command.Connection.State != System.Data.ConnectionState.Open)
                    {
                        await command.Connection.OpenAsync();
                    }
                    
                    // Execute and get result
                    var result = await command.ExecuteScalarAsync();
                    int count = Convert.ToInt32(result);
                    
                    return count > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error checking if column {columnName} exists: {ex.Message}");
                return false; // Assume it doesn't exist
            }
        }
    }
} 