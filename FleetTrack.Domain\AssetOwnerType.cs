﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class AssetOwnerType
{
    public int AssetOwnerTypeID { get; set; }

    public string AssetOwnerTypeName { get; set; } = null!;

    public string? AssetOwnerTypeDescription { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual ICollection<Asset> Assets { get; set; } = new List<Asset>();
}