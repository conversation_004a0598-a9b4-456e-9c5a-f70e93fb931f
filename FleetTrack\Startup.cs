using AtlasCommon.Infrastructure.Extensions;
using AtlasCommon.Infrastructure.Interceptors;
using FleetTrack.Data.Atlas;
using Autofac;
using CardManagementClient;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using NetCore.AutoRegisterDi;
using Serilog;
using Serilog.Extensions.Autofac.DependencyInjection;
using Swashbuckle.AspNetCore.Filters;
using System;
using FleetTrack.Factories;
using FleetTrack.Extensions;
using FleetTrack.Handlers;
using FleetTrack.Middleware;
using FleetTrack.Repositories;
using FleetTrack.Data;
using FleetTrack.Services.Assets;
using FleetTrack.DependencyInjectionModules;

namespace FleetTrack
{
	public class Startup
	{
		public Startup(IConfiguration configuration)
		{
			Configuration = configuration;
		}

		public ILifetimeScope AutofacContainer { get; private set; }

		public IConfiguration Configuration { get; }

		// This method gets called by the runtime. Use this method to add services to the container.
		public void ConfigureServices(IServiceCollection services)
		{
			services.AddSerilogUiFromConfiguration(Configuration);

			services.AddControllersWithViews();

			services.AddSingleton<IConnectionString, ConnectionString>();
			services.AddSingleton<ICardManagementClientFactory, CardManagementClientFactory>();
			services.AddHttpClient<IHTTPCallMethods, HTTPCallMethods>();


			services.AddOptions<CardManagementClientOptions>().Bind(Configuration.GetSection("AtlasManageCredentials"));

			services.AddDbContext<AtlasContext>((services, options) =>
								options.UseSqlServer(Configuration.GetConnectionString("AtlasEf"), x => x.UseNetTopologySuite())
								.AddInterceptors(services.GetRequiredService<AuditInterceptor>())
			);

			services.AddAuthentication(options =>
			{
				options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
				options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
				options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
			}).AddJwtBearer(options =>
			{
				options.TokenValidationParameters = new TokenValidationParameters
				{
					ValidateIssuer = true,
					ValidateAudience = true,
					ValidateLifetime = true,
					ValidateIssuerSigningKey = true,
					ClockSkew = TimeSpan.Zero,
					ValidAudience = "all",
					ValidIssuer = "localhost",
					IssuerSigningKey = new SigningCredentials(
								new SymmetricSecurityKey(Base64UrlTextEncoder.Decode(Configuration["JwtConfig:Secret"])), "HS256", "HS256").Key
				};
				options.RequireHttpsMetadata = false;
				options.SaveToken = true;
			});

			services.RegisterAssemblyPublicNonGenericClasses()
					.AsPublicImplementedInterfaces();

			// In production, the Angular files will be served from this directory
			services.AddSpaStaticFiles(configuration =>
			{
				configuration.RootPath = "ClientApp/dist";
			});

			services.AddAtlasCommon().AddEmail(options =>
			{
				Configuration.GetSection("SmtpEmailClientOptions").Bind(options);
				if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Production")
				{
					options.UseNetwork();
				}
				else
				{
					options.UseDirectory();
				}
			}).AddAuditing();
			services.AddSqlKata(Configuration["ConnectionStrings:AtlasSk"]);

			services.AddSwaggerGen(options =>
			{
				options.CustomSchemaIds(type => type.FullName);
				options.OperationFilter<AppendAuthorizeToSummaryOperationFilter>();
				options.OperationFilter<SecurityRequirementsOperationFilter>("Bearer");
				options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
				{
					Description = "Enter your JWT token and it will be provided in request header as Authorization: Bearer {Value}. You can request a token with /PhoneServiceNoAuth/Authenticate",
					In = ParameterLocation.Header,
					Name = "Authorization",
					Type = SecuritySchemeType.Http,
					Scheme = "Bearer",
				});
			});

			services.AddScoped<IEquipmentSecureLocationTypeService, EquipmentSecureLocationTypeService>();
		}

		// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
		public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
		{
			var useDeveloperExceptionPage = env.IsLocal() || env.IsDevelopment();

			if (useDeveloperExceptionPage)
			{
				app.UseDeveloperExceptionPage();
			}
			else
			{
				app.UseExceptionHandler("/Error");
				// The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
				app.UseHsts();
			}

			app.UseMiddleware<ExceptionHandlerMiddleware>(useDeveloperExceptionPage);

			app.UseHttpsRedirection();

			if (env.IsLocal() is false)
			{
				app.UseStaticFiles();
				app.UseSpaStaticFiles();
			}

			app.UseMiddleware<JwtMiddleware>();

			if (env.EnableSwagger())
			{
				app.UseSwagger();
				app.UseSwaggerUI();
			}

			app.UseAuthentication();
			app.UseRouting();
			app.UseAuthorization();

			app.UseSerilogUIWithCustomAuthOptions();

			app.UseEndpoints(endpoints =>
			{
				endpoints.MapControllerRoute(
						name: "default",
						pattern: "{controller}/{action=Index}/{id?}");
			});

			app.UseSpa(spa =>
			{
				// To learn more about options for serving an Angular SPA from ASP.NET Core,
				// see https://go.microsoft.com/fwlink/?linkid=864501

				spa.Options.SourcePath = "ClientApp";

				if (env.IsLocal())
				{
					//spa.UseAngularCliServer(npmScript: "start"); // Disabled to speed up development. Run Angular site in Visual Studio Code.
					spa.UseProxyToSpaDevelopmentServer("http://localhost:4200");
					//spa.UseProxyToSpaDevelopmentServer("http://localhost:34927");
				}
			});
		}

		public void ConfigureContainer(ContainerBuilder containerBuilder)
		{
			var connectionString = new ConnectionString(Configuration);

			containerBuilder.RegisterInstance(connectionString)
				.As<IConnectionString>()
				.SingleInstance();

			containerBuilder.RegisterType<MetaTypeStore>()
				.As<IMetaTypeStore>()
				.SingleInstance();

			containerBuilder.RegisterSerilog(new LoggerConfiguration().ReadFrom.Configuration(Configuration));

			containerBuilder.RegisterGeneric(typeof(GenericRepository<>))
				.As(typeof(IGenericRepository<>))
				.InstancePerLifetimeScope();

			containerBuilder.RegisterGeneric(typeof(SearchResponseFactory<>))
				.As(typeof(ISearchResponseFactory<>))
				.InstancePerLifetimeScope();

			containerBuilder.RegisterModule(new ContractorModule());
			containerBuilder.RegisterModule(new VendorModule());
			containerBuilder.RegisterModule(new LocationModule());
			containerBuilder.RegisterModule(new LegalityModule());
			containerBuilder.RegisterModule(new EnterpriseModule());
			containerBuilder.RegisterModule(new FacilityLocationModule());
			containerBuilder.RegisterModule(new RouteRateModule());
			containerBuilder.RegisterModule(new AgreementModule());
			containerBuilder.RegisterModule(new PrivilegeModule());
			containerBuilder.RegisterModule(new PersonnelModule());
			containerBuilder.RegisterModule(new AssetModule());
			containerBuilder.RegisterModule(new AssetNotesModule());
			containerBuilder.RegisterModule(new VaultModule());
			//containerBuilder.RegisterModule(new DocumentModule());
			containerBuilder.RegisterModule(new DashboardModule());
			containerBuilder.RegisterModule(new CarrierModule());
			containerBuilder.RegisterModule(new MoneyCardMaintenance());
			containerBuilder.RegisterModule(new CreditRequestModule());
		}
	}
}
