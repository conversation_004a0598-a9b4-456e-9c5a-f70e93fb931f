﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentCancellationReason
{
    public int EquipmentCancellationReasonID { get; set; }

    public string EquipmentCancellationReasonName { get; set; } = null!;

    public string? EquipmentCancellationReasonDescription { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual ICollection<Equipment> Equipment { get; set; } = new List<Equipment>();
}