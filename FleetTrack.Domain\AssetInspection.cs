﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class AssetInspection
{
    public int AssetInspectionID { get; set; }

    public int AssetID { get; set; }

    public DateTime InspectionDate { get; set; }

    public DateTime? NextInspectionDate { get; set; }

    public int? PaidByFacilityID { get; set; }

    public int? PaidByContractorID { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public int? PaidByEnterpriseID { get; set; }

    public virtual Asset Asset { get; set; } = null!;

    public virtual Enterprise? PaidByEnterprise { get; set; }
}