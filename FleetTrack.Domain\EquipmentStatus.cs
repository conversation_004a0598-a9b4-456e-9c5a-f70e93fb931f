﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class EquipmentStatus
{
    public int EquipmentStatusID { get; set; }

    public string EquipmentStatusName { get; set; } = null!;

    public string EquipmentStatusCode { get; set; } = null!;

    public string? EquipmentStatusDescription { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public virtual ICollection<Equipment> Equipment { get; set; } = new List<Equipment>();
}