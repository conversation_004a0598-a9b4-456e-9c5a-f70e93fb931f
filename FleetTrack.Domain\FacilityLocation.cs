﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class FacilityLocation
{
    public int FacilityLocationID { get; set; }

    public int LocationID { get; set; }

    public int? EnterpriseID { get; set; }

    public string? FacilityLocationCode { get; set; }

    public string? FacilityLocationName { get; set; }

    public string? FacilityLocationDescription { get; set; }

    public int? FacilityLocationTypeID { get; set; }

    public DateTime FacilityLocationStartDate { get; set; }

    public DateTime? FacilityLocationCloseDate { get; set; }

    public bool IsSecureDropyard { get; set; }

    public bool? IsActive { get; set; }

    public bool IsUnion { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public int? FacilityOwnerTypeID { get; set; }

    public bool IsBookingHauling { get; set; }

    public int? MailingLocationID { get; set; }

    public bool IsUsable { get; set; }

    public virtual ICollection<Asset> Assets { get; set; } = new List<Asset>();

    public virtual Enterprise? Enterprise { get; set; }

    public virtual Location Location { get; set; } = null!;

    public virtual Location? MailingLocation { get; set; }
}