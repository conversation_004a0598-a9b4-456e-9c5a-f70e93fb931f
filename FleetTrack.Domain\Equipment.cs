﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;

namespace FleetTrack.Domain;

public partial class Equipment
{
    public int EquipmentID { get; set; }

    public int? AssetID { get; set; }

    public int EquipmentTypeID { get; set; }

    public int? EquipmentModelID { get; set; }

    public string? SerialNumber { get; set; }

    public double? UnladenWeight { get; set; }

    public double? MaximumGrossWeight { get; set; }

    public int? EquipmentPowerAttributeID { get; set; }

    public int? EquipmentRollingAttributeID { get; set; }

    public int? EquipmentStorageAttributeID { get; set; }

    public int? EquipmentPulledAttributeID { get; set; }

    public int? EquipmentReeferAttributeID { get; set; }

    public bool IsSafetyShutdown { get; set; }

    public int? SafetyShutdownBy { get; set; }

    public int? SafetyShutdownReasonID { get; set; }

    public int? EquipmentCancellationReasonID { get; set; }

    public int? EquipmentCancellationByID { get; set; }

    public DateTime CreatedDate { get; set; }

    public int CreatedByID { get; set; }

    public int? ModifiedByID { get; set; }

    public DateTime? SafetyShutdownDate { get; set; }

    public int? EquipmentStatusID { get; set; }

    public DateTime? EquipmentStatusDate { get; set; }

    public string? SafetyShutdownComment { get; set; }

    public virtual Asset? Asset { get; set; }

    public virtual EquipmentCancellationReason? EquipmentCancellationReason { get; set; }

    public virtual ICollection<EquipmentMaintenance> EquipmentMaintenances { get; set; } = new List<EquipmentMaintenance>();

    public virtual EquipmentModel? EquipmentModel { get; set; }

    public virtual ICollection<EquipmentPlate> EquipmentPlates { get; set; } = new List<EquipmentPlate>();

    public virtual EquipmentPowerAttribute? EquipmentPowerAttribute { get; set; }

    public virtual EquipmentPulledAttribute? EquipmentPulledAttribute { get; set; }

    public virtual EquipmentReeferAttribute? EquipmentReeferAttribute { get; set; }

    public virtual EquipmentRollingAttribute? EquipmentRollingAttribute { get; set; }

    public virtual ICollection<EquipmentSecureLocation> EquipmentSecureLocations { get; set; } = new List<EquipmentSecureLocation>();

    public virtual EquipmentStatus? EquipmentStatus { get; set; }

    public virtual EquipmentStorageAttribute? EquipmentStorageAttribute { get; set; }

    public virtual EquipmentType EquipmentType { get; set; } = null!;

    public virtual ICollection<PlateHistory> PlateHistories { get; set; } = new List<PlateHistory>();

    public virtual SafetyShutdownReason? SafetyShutdownReason { get; set; }
}